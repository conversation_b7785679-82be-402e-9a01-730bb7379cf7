["ibd2sdi", {"type": 1, "id": 936, "object": {"mysqld_version_id": 80033, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Table", "dd_object": {"name": "base_code", "mysql_version_id": 80033, "created": 20240218071017, "last_altered": 20240218071017, "hidden": 1, "options": "avg_row_length=0;encrypt_type=N;key_block_size=0;keys_disabled=0;pack_record=1;stats_auto_recalc=0;stats_sample_pages=0;", "columns": [{"name": "编号版本", "type": 15, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 1, "char_length": 10, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "default_null=1;physical_pos=33;table_id=1463;version_added=1;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "date", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "组合装编号", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 2, "char_length": 1020, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=3;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(255)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "GSS（包）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 3, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=4;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "GPD（包）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 4, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=5;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "IIP（包）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 5, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=6;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "IHAG（包）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 6, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=7;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "SM（包）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 7, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=8;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "CGSSJ（杯）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 8, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=9;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "玉米片（包）烧烤味", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 9, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=10;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "玉米片（包）奶酪味", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 10, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=11;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "椰子水（瓶）", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 11, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=12;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "赠品名称", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 12, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=13;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "赠品数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 13, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=14;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "礼盒名称", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 14, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=15;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "礼盒数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 15, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=16;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "塑封膜", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 16, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=17;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "品类", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 17, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=18;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "口味规格", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 18, "char_length": 400, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=19;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(100)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "袋面单混口味", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 19, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=20;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "汤面单混口味", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 20, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=21;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "袋汤面单混口味", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 21, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=22;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "玉米片单混口味", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 22, "char_length": 120, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=23;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(30)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "袋面规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 23, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=24;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "汤面规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 24, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=25;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "袋汤面规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 25, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=26;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "杯面规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 26, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=27;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "玉米片规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 27, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=28;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "椰子水规格", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 28, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=29;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "袋面整散装", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 29, "char_length": 80, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=30;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(20)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "汤面整散装", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 30, "char_length": 80, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=31;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(20)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "袋汤面整散装", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 31, "char_length": 80, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "physical_pos=32;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(20)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "DB_ROW_ID", "type": 10, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 32, "char_length": 6, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "physical_pos=0;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}, {"name": "DB_TRX_ID", "type": 10, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 33, "char_length": 6, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "physical_pos=1;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}, {"name": "DB_ROLL_PTR", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 34, "char_length": 7, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "physical_pos=2;table_id=1463;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}], "schema_ref": "qq_day_sale", "se_private_id": 1463, "engine": "InnoDB", "last_checked_for_upgrade_version_id": 0, "comment": "", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "row_format": 2, "partition_type": 0, "partition_expression": "", "partition_expression_utf8": "", "default_partitioning": 0, "subpartition_type": 0, "subpartition_expression": "", "subpartition_expression_utf8": "", "default_subpartitioning": 0, "indexes": [{"name": "PRIMARY", "hidden": true, "is_generated": false, "ordinal_position": 1, "comment": "", "options": "", "se_private_data": "id=591;root=4;space_id=401;table_id=1463;trx_id=196126;", "type": 2, "algorithm": 2, "is_algorithm_explicit": false, "is_visible": true, "engine": "InnoDB", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 31}, {"ordinal_position": 2, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 32}, {"ordinal_position": 3, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 33}, {"ordinal_position": 4, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 0}, {"ordinal_position": 5, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 1}, {"ordinal_position": 6, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 2}, {"ordinal_position": 7, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 3}, {"ordinal_position": 8, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 4}, {"ordinal_position": 9, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 5}, {"ordinal_position": 10, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 6}, {"ordinal_position": 11, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 7}, {"ordinal_position": 12, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 8}, {"ordinal_position": 13, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 9}, {"ordinal_position": 14, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 10}, {"ordinal_position": 15, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 11}, {"ordinal_position": 16, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 12}, {"ordinal_position": 17, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 13}, {"ordinal_position": 18, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 14}, {"ordinal_position": 19, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 15}, {"ordinal_position": 20, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 16}, {"ordinal_position": 21, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 17}, {"ordinal_position": 22, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 18}, {"ordinal_position": 23, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 19}, {"ordinal_position": 24, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 20}, {"ordinal_position": 25, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 21}, {"ordinal_position": 26, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 22}, {"ordinal_position": 27, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 23}, {"ordinal_position": 28, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 24}, {"ordinal_position": 29, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 25}, {"ordinal_position": 30, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 26}, {"ordinal_position": 31, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 27}, {"ordinal_position": 32, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 28}, {"ordinal_position": 33, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 29}, {"ordinal_position": 34, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 30}], "tablespace_ref": "qq_day_sale/base_code"}], "foreign_keys": [], "check_constraints": [], "partitions": [], "collation_id": 255}}}, {"type": 2, "id": 406, "object": {"mysqld_version_id": 80033, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Tablespace", "dd_object": {"name": "qq_day_sale/base_code", "comment": "", "options": "autoextend_size=0;encryption=N;", "se_private_data": "flags=16417;id=401;server_version=80033;space_version=1;state=normal;", "engine": "InnoDB", "engine_attribute": "", "files": [{"ordinal_position": 1, "filename": ".\\qq_day_sale\\base_code.ibd", "se_private_data": "id=401;"}]}}}]