USE qq_day_sale;

CREATE TABLE IF NOT EXISTS base_code (
    `编号版本` DATE,
    `组合装编号` VARCHAR(255),
    `GSS（包）` INT,
    `GPD（包）` INT,
    `IIP（包）` INT,
    `IHAG（包）` INT,
    `SM（包）` INT,
    `CGSSJ（杯）` INT,
    `玉米片（包）烧烤味` INT,
    `玉米片（包）奶酪味` INT,
    `椰子水（瓶）` INT,
    `赠品名称` VARCHAR(255),
    `赠品数量` INT,
    `礼盒名称` VARCHAR(255),
    `礼盒数量` INT,
    `塑封膜` VARCHAR(255),
    `品类` VARCHAR(255),
    `口味规格` VARCHAR(255),
    `袋面单混口味` VARCHAR(255),
    `汤面单混口味` VARCHAR(255),
    `袋汤面单混口味` VARCHAR(255),
    `玉米片单混口味` VARCHAR(255),
    `袋面规格` VARCHAR(255),
    `汤面规格` VARCHAR(255),
    `袋汤面规格` VARCHAR(255),
    `杯面规格` VARCHAR(255),
    `玉米片规格` VARCHAR(255),
    `椰子水规格` VARCHAR(255),
    `袋面整散装` VARCHAR(255),
    `汤面整散装` VARCHAR(255),
    `袋汤面整散装` VARCHAR(255),
    PRIMARY KEY (`编号版本`, `组合装编号`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;