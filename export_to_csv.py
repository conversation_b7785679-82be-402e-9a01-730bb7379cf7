#!/usr/bin/env python3
import subprocess
import csv
import json

# Execute MySQL query and get JSON output
cmd = [
    '/opt/homebrew/opt/mysql@8.0/bin/mysql',
    '-uroot',
    '-psa2482047260',
    '--default-character-set=utf8mb4',
    '-e',
    'USE qq_day_sale; SELECT * FROM base_code;',
    '--batch',
    '--raw'
]

result = subprocess.run(cmd, capture_output=True, text=True)

if result.returncode != 0:
    print(f"Error: {result.stderr}")
    exit(1)

# Parse the output
lines = result.stdout.strip().split('\n')
if len(lines) < 2:
    print("No data found")
    exit(1)

# First line contains headers
headers = lines[0].split('\t')

# Write to CSV
with open('/Users/<USER>/Library/CloudStorage/OneDrive-MSFT/Project shared/修复/base_code_export.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
    writer = csv.writer(csvfile)
    
    # Write headers
    writer.writerow(headers)
    
    # Write data rows
    for line in lines[1:]:
        row = line.split('\t')
        writer.writerow(row)

print(f"Successfully exported {len(lines)-1} records to base_code_export.csv")